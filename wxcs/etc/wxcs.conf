# basic
SERVER_PORT                       = 1024
RTL_TAGGED_MODE                   = 1               # 是否有 标签
DROP_UNCREDIBLE_RECORD            = 0               # 是否丢弃“不可信记录”，即"三元组"全者
LOG_MAX_SIZE                      = 1073741824      # 大小设置为 1G
LOG_INTEREST_MAX_SIZE             = 1073741824      # 大小设置为 1G
THREAD_COUNT_IN_THREAD_POOL       = 4                # TcpServer内部线程池的线程个数

# session
WXA_HASH_TABLE_SIZE               = 5000000         # wxa 会话表大小，500万
WXA_RECOGNITION                   = 1               # 1 精确识别  0 模糊识别
ZOOM_SESSION_TIMEOUT_IN_SECOND    = 60              # ZOOM会话若干秒未被刷新即认为会话结束(必须大于dpi超时周期!!!)
WXA_SESSION_TIMEOUT_IN_SECOND     = 45              # wxa 会话若干秒未被刷新即认为会话结束(必须大于dpi超时周期!!!)
QQF_SESSION_TIMEOUT_IN_SECOND     = 65              # QQ文件 会话若干秒未被刷新即认为会话结束
WXGH_SESSION_TIMEOUT_IN_SECOND    = 90              # wxgh 会话 90s 未被刷新即认为会话结束
QQAV_SESSION_TIMEOUT_IN_SECOND    = 15                # qqav 会话 30s 未被刷新即认为会话结束
TENCENT_MEETING_SESSION_TIMEOUT_IN_SECOND    = 15                # qqav 会话 30s 未被刷新即认为会话结束
SESSION_CHECK_INTERVAL_IN_SECONDS = 5               # 会话超时检测间隔



# session management policy
SESSION_PACKET_AT_LEAST           = 0               # 每个会话至少总计达到多少包数才认为其有效，输出到 tbl
SESSION_PACKET_VIDEO_NUM          = 10              # 上下行任意一方,视频包个数大于此值, 判定为视频通话类型
SESSION_ID_WXA_ENABLE             = 1               # 是否输出 WXA SESSION ID

# drop policy
QQ_GROUP_MIN_PERSON_COUNT         = 1              # QQ群通话人数小于该值不输出
QQ_GROUP_MAX_PERSON_COUNT         = 500              # QQ群通话人数大于该值不输出
QQ_MIN_NUM                        = 0          # 小于该值的QQ号不处理
QQ_MAX_NUM                        = 100000000000   # 大于该值的QQ号不处理

# tbl
TBL_FIELD_DIR                     = "/root/program/field"
TBL_OUTPUT_DIR                    = "/tmp/tbls"
TBL_FILE_NAME_MAX_FORMAT_CODE     = 4              # tbl及内容实体文件的文件名编码参数的范围[0, MAX_FILE_NAME_CODE)
TBL_FILE_LINE                     = 50             # tbl 条目累积到多少条后更换一个文件
TBL_FILE_LINE_QQ                  = 50             # qq话单的tbl 条目累积到多少条后更换一个文件
WXP_TBL_FILE_LINE                 = 5              # 微信位置分享的tbl 条目累积到多少条后更换一个文件
WRITE_TBL_MAXTIME                 = 65             # tbl.writing 存在的最长时间，如果没有达到写tbl最大条数，过了这个时间，则重命名为tbl
TBL_FILE_PUSH_FLG                 = 1              # tbl推送标志，0默认推送，1:不推送 (根据推送标志TBL中个别字段填入数据不同)
TBL_OUT_MISSED_CALLS              = 0              # 是否输出未接听话单 0:不输出， 1:输出

# image
WX_GROUP_HEAD_IMG_DIR             = "/tmp/tbls/wxgh_img"

# log level: "trace", "debug", "info", "warning", "error", "critical", "off"
LOG_TO_FILE                       = 0               # 是否将日志记录到文件，否则打印到终端
LOG_LEVEL                         = debug

#监视设置1:监视的 MSISDN 列表
# INTERESTING_MSISDN                = 8617717381843,8618917715310

#监视设置2:监视的 IMSI 列表
#INTERESTING_IMSI                  = 460110128271191,460110129622691

#监视设置3:监视的 IMEI 列表
#INTERESTING_IMEI                  = 86846403521169,86684803645072


#WXA WEB 演示发送到页面的开关
SEND_WXA_UDP = 1