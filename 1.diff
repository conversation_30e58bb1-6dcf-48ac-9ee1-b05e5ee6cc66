diff --git a/etc/config.ini b/etc/config.ini
index eed93e6..fb607a8 100644
--- a/etc/config.ini
+++ b/etc/config.ini
@@ -8,19 +8,19 @@ CMD_LINE = "./yaWxDpi -l 0-2 -- -r 1,2 -p 3,4,5,6,7,8 -l 9,10,11"
 # 解析线程可根据计算版cpu核心进行配置，使用命令`lscpu`查看 解析线程不可跨NUMA核心
 
 # 开发机专用配置: 网卡只有1个队列,小内存 #
-#CMD_LINE = "./yaWxDpi -l 0-2 -- -r 1 -p 3 -l 6"
+CMD_LINE = "./yaWxDpi -l 0-2 -- -r 1 -p 3 -l 6"
 #CMD_LINE = "gdb --args ./yaWxDpi --vdev="pcap_recur,dir=/home/<USER>/pcaps/pc/qq/20250528/test,pps=20k" -l 0-2 -- -r 1 -p 3 -l 6"
-#MEMPOOL_CACHE_SIZE         = 256
-#PACKET_RING_SIZE           = 1024
-#MAX_PKT_LEN                = 2000
-#MAX_FLOW_NUM               = 1024
-#MAX_CONVERSATION_HASH_NODE = 256
-#TBL_RING_SIZE              = 1024
-#TBL_LOG_CONTENT_256K       = 1024               # default value: 262144
-#LUA_ADAPT_DIR        = ./lua_adapt/
-#LUA_ADAPT_SCRIPT_TYPE  = .lua
-#NB_MBUF = 1024                                                    # default value: 1048576  ,minimum value 512
-#TCP_REASSEMBLE_MEMPOOL_NUM = 1024                                 # default value: 1048576  ,必须为大于 768 的 2 的幂 ,minimum value 512
+MEMPOOL_CACHE_SIZE         = 256
+PACKET_RING_SIZE           = 1024
+MAX_PKT_LEN                = 2000
+MAX_FLOW_NUM               = 1024
+MAX_CONVERSATION_HASH_NODE = 256
+TBL_RING_SIZE              = 1024
+TBL_LOG_CONTENT_256K       = 1024               # default value: 262144
+LUA_ADAPT_DIR        = ./lua_adapt/
+LUA_ADAPT_SCRIPT_TYPE  = .lua
+NB_MBUF = 1024                                                    # default value: 1048576  ,minimum value 512
+TCP_REASSEMBLE_MEMPOOL_NUM = 1024                                 # default value: 1048576  ,必须为大于 768 的 2 的幂 ,minimum value 512
 # DPDK 相关参数
 RXQ_NUM              = 4
 DISSECTOR_THREAD_NUM = 16
diff --git a/include/wxcs_def.h b/include/wxcs_def.h
index c4eefea..5a59a5b 100644
--- a/include/wxcs_def.h
+++ b/include/wxcs_def.h
@@ -148,6 +148,7 @@ enum
     WXCS_WX_PEERS,          /* wx 一对一通话 公网ip */
     WXCS_QQ_EVENT,         /* ZOOM 会议行为 */
     WXCS_WX_LOC_SHARING,    /* wx 共享实时位置 */
+    WXCS_TENCENT_MEETING,   /* 腾讯会议*/
     WXCS_ENUM_MAX,
 };
 
@@ -613,6 +614,40 @@ typedef struct wx_location_shared_
 /*=============================== wx 共享实时位置 ============================================================*/
 
 
+/*=============================== 腾讯会议  ============================================================*/
+#define TENCENT_MEETING_MAX_MEMBER_COUNT 10   /*! 群通话成员最大输出数量 */
+
+typedef struct {
+    // 建联信息, 必须置于消息体的头部
+    ST_trailer trailer;
+
+    uint8_t  ip_version;
+    uint32_t srcIp;
+    uint8_t  srcIp6[16];
+    uint32_t dstIp;
+    uint8_t  dstIp6[16];
+
+    uint16_t srcPort;
+    uint16_t dstPort;
+  
+    uint32_t c2sPackCount;
+    uint32_t c2sByteCount;
+    uint32_t s2cPackCount;
+    uint32_t s2cByteCount;
+
+    uint64_t sessionId;
+    char selfMeetingNum[18];
+    char selfLoginNum[18];
+
+    uint32_t firstActiveTime;
+    uint32_t lastActiveTime;
+    uint8_t  isTimeout;                         // 这个人是否已经超时
+} ST_TencentMeeting;
+
+/*=============================== 腾讯会议 ============================================================*/
+
+
+
 
 #define  WX_PYQ_JPG  0
 #define  WX_PYQ_MP4  1
diff --git a/src/PROTOCOL_LIST.txt b/src/PROTOCOL_LIST.txt
index 597e905..ff20f92 100644
--- a/src/PROTOCOL_LIST.txt
+++ b/src/PROTOCOL_LIST.txt
@@ -27,3 +27,4 @@ DOUYIN
 WXID
 WXPAY
 ALIPAY
+TENCENT_MEETING
\ No newline at end of file
diff --git a/src/dpi_main.c b/src/dpi_main.c
index ed2275f..9428320 100644
--- a/src/dpi_main.c
+++ b/src/dpi_main.c
@@ -1613,6 +1613,8 @@ static __attribute((constructor)) void     before_main_init(void){
     register_tbl_array(TBL_LOG_WEIXIN_PYQ, 				0, "weixin_pyq2", 		NULL);
 
 	register_tbl_array(TBL_LOG_RELATION, 						0, "relation", 			NULL);
+  register_tbl_array(TBL_LOG_TENCENT_MEETING, 			0, 	"TencentMeeting", 		init_tencent_meeting_chat_dissector);
+
 	// register_tbl_array(TBL_LOG_GQUIC_WEIXIN_F, 			0, "gquic_wx", 		NULL);
 	// register_tbl_array(TBL_LOG_GQUIC_WEIXIN_PYQ, 		0, "gquic_wx_pyq", 	NULL);
 
diff --git a/src/framework/dpi_dissector.h b/src/framework/dpi_dissector.h
index d744789..02fa305 100644
--- a/src/framework/dpi_dissector.h
+++ b/src/framework/dpi_dissector.h
@@ -36,4 +36,5 @@ void init_qq_event_dissector(void);
 void init_weixin_misc_dissector(void);
 void init_gquic_dissector(void);
 void fini_http_dissector(void);
+void init_tencent_meeting_chat_dissector(void);
 #endif
diff --git a/src/framework/dpi_proto_ids.c b/src/framework/dpi_proto_ids.c
index bfd0f0e..70a436d 100644
--- a/src/framework/dpi_proto_ids.c
+++ b/src/framework/dpi_proto_ids.c
@@ -35,6 +35,7 @@ const char *protocol_name_array[PROTOCOL_MAX] =
     "WXID",
     "WXPAY",
     "ALIPAY",
+    "TENCENT_MEETING",
 };
 
 
diff --git a/src/framework/dpi_proto_ids.h b/src/framework/dpi_proto_ids.h
index 68c586c..23d0dbd 100644
--- a/src/framework/dpi_proto_ids.h
+++ b/src/framework/dpi_proto_ids.h
@@ -36,6 +36,7 @@ enum tbl_log_type {
     TBL_LOG_WXID,
     TBL_LOG_WXPAY,
     TBL_LOG_ALIPAY,
+    TBL_LOG_TENCENT_MEETING,
     TBL_LOG_MAX
 };
 
@@ -72,6 +73,7 @@ enum PROTOCOL_TYPE {
     PROTOCOL_WXID,
     PROTOCOL_WXPAY,
     PROTOCOL_ALIPAY,
+    PROTOCOL_TENCENT_MEETING,
     PROTOCOL_MAX
 };
 
diff --git a/src/proto/CMakeLists.txt b/src/proto/CMakeLists.txt
index ba137b6..bc866b1 100644
--- a/src/proto/CMakeLists.txt
+++ b/src/proto/CMakeLists.txt
@@ -26,5 +26,5 @@ add_library(proto OBJECT
   dpi_wxpay_manager.cpp
   dpi_wx_voice_peers.c
   dpi_zoom_conference.c
-
+  dpi_tencent_meeting.c
 )
diff --git a/src/utils/dpi_utils.c b/src/utils/dpi_utils.c
index 6a0960d..37c0b27 100644
--- a/src/utils/dpi_utils.c
+++ b/src/utils/dpi_utils.c
@@ -220,3 +220,15 @@ char* dpi_utils_get_ipv4_addr_of_if(const char *interface_name)
 
     return ip_address;
 }
+int get_ipstring(uint8_t version, char *input, char *output, uint16_t o_len)
+{
+  int af = 0;
+  if (version == 4) {
+    af = AF_INET;
+  } else if (version == 6) {
+    af = AF_INET6;
+  }
+  inet_ntop(af, input, output, o_len);
+
+  return 0;
+}
\ No newline at end of file
diff --git a/src/utils/dpi_utils.h b/src/utils/dpi_utils.h
index ec4e663..899dc2c 100644
--- a/src/utils/dpi_utils.h
+++ b/src/utils/dpi_utils.h
@@ -31,6 +31,7 @@ extern "C"
 int dpi_utils_strftime(char buff[], int buff_size, const char *format);
 
 char* dpi_utils_get_ipv4_addr_of_if(const char *interface_name);
+int get_ipstring(uint8_t version, char *input, char *output, uint16_t o_len);
 
 #ifdef __cplusplus
 }
diff --git a/wxcs/etc/wxcs.conf b/wxcs/etc/wxcs.conf
index d545966..84aa3a2 100644
--- a/wxcs/etc/wxcs.conf
+++ b/wxcs/etc/wxcs.conf
@@ -14,6 +14,7 @@ WXA_SESSION_TIMEOUT_IN_SECOND     = 45              # wxa 会话若干秒未被
 QQF_SESSION_TIMEOUT_IN_SECOND     = 65              # QQ文件 会话若干秒未被刷新即认为会话结束
 WXGH_SESSION_TIMEOUT_IN_SECOND    = 90              # wxgh 会话 90s 未被刷新即认为会话结束
 QQAV_SESSION_TIMEOUT_IN_SECOND    = 15                # qqav 会话 30s 未被刷新即认为会话结束
+TENCENT_MEETING_SESSION_TIMEOUT_IN_SECOND    = 15                # qqav 会话 30s 未被刷新即认为会话结束
 SESSION_CHECK_INTERVAL_IN_SECONDS = 5               # 会话超时检测间隔
 
 
diff --git a/wxcs/wxcs_person.cpp b/wxcs/wxcs_person.cpp
index e447316..2d81e9f 100644
--- a/wxcs/wxcs_person.cpp
+++ b/wxcs/wxcs_person.cpp
@@ -217,7 +217,16 @@ std::string WxcsPerson::getPrintablePersonMobileID() const
         return std::to_string(MSISDN) + "," + std::to_string(IMEI) + "," + std::to_string(IMSI);
     }
 
-    return inet_ntoa(*(struct in_addr*)(&this->client_ip));
+    char burrer_ip[64];
+
+    if (4 == ip_version) {
+        return inet_ntop(AF_INET, &client_ip, burrer_ip, 64);
+    }
+
+    if (6 == ip_version) {
+        return inet_ntop(AF_INET6, client_ipv6, burrer_ip, 64);
+    }
+    return "";
 }
 
 std::string WxcsPerson::getPrintableSessionID() const
@@ -1182,3 +1191,70 @@ int WxcsQQEventPerson::getAllMsisdnQQ(std::map<size_t, std::map<size_t, QQEventI
 /*********************************QQevent****************************************************/
 
 /*ADD_E by yangna 2020-08-20 */
+
+//////////////////// WxcsTencentMeetingPerson //////////////////////////////////////////
+WxcsTencentMeetingPerson::WxcsTencentMeetingPerson()
+{
+}
+
+
+WxcsTencentMeetingPerson::WxcsTencentMeetingPerson(ST_TencentMeeting *pMeetingMsg)
+    : WxcsPerson((uint8_t*)&pMeetingMsg->sessionId, sizeof(pMeetingMsg->sessionId), 0,
+                 pMeetingMsg->srcPort, pMeetingMsg->dstPort,
+                 pMeetingMsg->srcIp, pMeetingMsg->dstIp,
+                 pMeetingMsg->lastActiveTime,
+                 &pMeetingMsg->trailer)
+    , sessionId(pMeetingMsg->sessionId)
+    , selfMeetingNum(pMeetingMsg->selfMeetingNum)
+    , selfLoginNum(pMeetingMsg->selfLoginNum)
+    , firstActiveTime(pMeetingMsg->firstActiveTime)
+    , lastActiveTime(pMeetingMsg->lastActiveTime)
+    , c2sPackCount(pMeetingMsg->c2sPackCount)
+    , c2sByteCount(pMeetingMsg->c2sByteCount)
+    , s2cPackCount(pMeetingMsg->s2cPackCount)
+    , s2cByteCount(pMeetingMsg->s2cByteCount)
+    , isTimeout(pMeetingMsg->isTimeout)
+{
+    if (pMeetingMsg->dstIp6[0] != 0) {
+        memcpy(server_ipv6, pMeetingMsg->dstIp6, sizeof(client_ipv6));
+    }
+    if (pMeetingMsg->srcIp6[0] != 0) {
+        memcpy(client_ipv6, pMeetingMsg->srcIp6, sizeof(client_ipv6));
+    }
+}
+
+std::string WxcsTencentMeetingPerson::toStrRecord(char sep) const
+{
+    std::string strRecord;
+    strRecord += getStrTrailer(sep);  // 建联信息
+
+    // session info
+    CONS_RECORD_FIELD_TEXT(strRecord,  selfLoginNum, sep);
+    CONS_RECORD_FIELD_TEXT(strRecord,  selfMeetingNum, sep);
+
+    // statistics
+    CONS_RECORD_FIELD_NUM(strRecord,   c2sPackCount, sep);
+    CONS_RECORD_FIELD_NUM(strRecord,   s2cPackCount, sep);
+    CONS_RECORD_FIELD_NUM(strRecord,   c2sByteCount, sep);
+    CONS_RECORD_FIELD_NUM(strRecord,   s2cByteCount, sep);
+
+    // date
+    CONS_RECORD_FIELD_TIME(strRecord,  firstActiveTime, sep);
+    CONS_RECORD_FIELD_TIME(strRecord,  lastActiveTime, sep);
+
+    //保留字段
+    CONS_RECORD_FIELD_TEXT(strRecord,  "", sep);
+
+    return strRecord;
+}
+
+std::string WxcsTencentMeetingPerson::toStrBlankRecord(char sep)
+{
+    std::string strRecord;
+    for (int i = 0; i < 31; i++)
+    {
+        strRecord += std::string("\"\"") + sep;
+    }
+
+    return strRecord;
+}
diff --git a/wxcs/wxcs_person.h b/wxcs/wxcs_person.h
index 03936dc..4750116 100644
--- a/wxcs/wxcs_person.h
+++ b/wxcs/wxcs_person.h
@@ -344,6 +344,29 @@ public:
     uint32_t  isTimeout;               // 是否已经超时
 };
 
+/* WxcsTencentMeetingPerson */
+class WxcsTencentMeetingPerson : public WxcsPerson
+{
+public:
+    WxcsTencentMeetingPerson();
+    WxcsTencentMeetingPerson(ST_TencentMeeting *pMeetingMsg);
+
+    virtual std::string toStrRecord(char sep) const;
+    static  std::string toStrBlankRecord(char sep);
+
+public:
+    uint64_t  sessionId;               // 会议SessionID
+    std::string selfMeetingNum;        // 个人会议账号
+    std::string selfLoginNum;          // 个人常规账号
+    uint32_t  firstActiveTime;         // 首次活跃时间
+    uint32_t  lastActiveTime;          // 最后活跃时间
+    uint32_t  c2sPackCount;            // C2S包数量
+    uint32_t  c2sByteCount;            // C2S字节数
+    uint32_t  s2cPackCount;            // S2C包数量
+    uint32_t  s2cByteCount;            // S2C字节数
+    uint8_t   isTimeout;               // 是否已经超时
+};
+
 /**/
 class WXRelation
 {
diff --git a/wxcs/wxcs_server.cpp b/wxcs/wxcs_server.cpp
index 15119ce..2c9a5b3 100644
--- a/wxcs/wxcs_server.cpp
+++ b/wxcs/wxcs_server.cpp
@@ -77,6 +77,10 @@ WxcsServer::WxcsServer(muduo::net::EventLoop *pLoop, uint16_t port, const std::s
                           CFG->GetValueOf<int>("SKYPE_HASH_TABLE_SIZE", SKYPE_HASH_TABLE_SIZE))
     , skypeTblWriter_(strTblDir, "skype", CFG->GetValueOf<int>("SKYPE_TBL_FILE_LINE", SKYPE_PER_TBL_LINE))
     , locSharingTblWrite_(strTblDir, "wxls", CFG->GetValueOf<int>("TBL_FILE_LINE", WXA_PER_TBL_LINE))
+
+    , tencentMeetingSessionKeeper_(CFG->GetValueOf<int>("TENCENT_MEETING_SESSION_TIMEOUT_IN_SECOND", SESSION_ACTIVE_TIME_DIFF_MAX),
+                                   CFG->GetValueOf<int>("TENCENT_MEETING_HASH_TABLE_SIZE", WXA_HASH_TABLE_SIZE))
+    , tencentMeetingTblWriter_(strTblDir, "tencent_meeting", CFG->GetValueOf<int>("TENCENT_MEETING_TBL_FILE_LINE", WXA_PER_TBL_LINE))
     , addr_(port)
     , server_(pLoop, addr_, "wxcserver")
     , pProtoCodec_(NULL)
@@ -122,7 +126,7 @@ WxcsServer::WxcsServer(muduo::net::EventLoop *pLoop, uint16_t port, const std::s
     QQFileTblWriter_.writeColumnsFile(field_dir, WxcsSession<WxcsQQFilePerson>::getColumnList('\n'));
     skypeTblWriter_.writeColumnsFile(field_dir, WxcsSession<WxcsSkypePerson>::getColumnList('\n'));
     locSharingTblWrite_.writeColumnsFile(field_dir, WxcsSession<WxcsLocSharingPerson>::getColumnList('\n'));
-
+    tencentMeetingTblWriter_.writeColumnsFile(field_dir, WxcsSession<WxcsTencentMeetingPerson>::getColumnList('\n'));
     LOG_DEF->info("I'm listening on port {}" , addr_.toIpPort());
 }
 
@@ -936,6 +940,63 @@ void WxcsServer::ProcessQQEvent(const unsigned char*pdata, int len)
     return;
 }
 
+void WxcsServer::ProcessTencentMeeting(const unsigned char*pdata, int len)
+{
+    if (len != sizeof(ST_TencentMeeting))
+    {
+        LOG_INTST->error("ProcessTencentMeeting len error, len={}, sizeof(ST_TecentMeeting)={}", len, sizeof(ST_TencentMeeting));
+        return;
+    }
+
+    ST_TencentMeeting *pMeetingMsg = (ST_TencentMeeting *)pdata;
+
+    // 创建腾讯会议Person对象
+    PersonPtr<WxcsTencentMeetingPerson> pPerson = std::make_shared<WxcsTencentMeetingPerson>(pMeetingMsg);
+
+    // 检测该用户记录是否可信，如不则丢弃，不创建会话，不添加用户
+    if (CFG->GetValueOf<bool>("DROP_UNCREDIBLE_RECORD", true) && pPerson->wasUncrediableId())
+    {
+        LOG_DEF->debug("tencent meeting: drop uncredible person record {}", pPerson->getPrintablePersonMobileID());
+        return ;
+    }
+
+    // 是不是监视的 手机号 ?
+    bool bInterestingPerson = interestingMsisdnSet_.find(pPerson->getMsisdn()) != interestingMsisdnSet_.end();
+    if (bInterestingPerson)
+    {
+        LOG_INTST->warn("tencent meeting: found interesting person msg : {} of session {}",
+                        pPerson->getMsisdn(),
+                        pPerson->getPrintableSessionID());
+    }
+
+    // 查找或创建session（使用sessionId作为session标识）
+    auto pTencentMeetingSession = tencentMeetingSessionKeeper_.getSessionOf((uint8_t*)&pMeetingMsg->sessionId, sizeof(pMeetingMsg->sessionId));
+    if (!pTencentMeetingSession)
+    {   // 添加新 session,向该 session 中添加用户
+        SessionPtr<WxcsTencentMeetingPerson> newSession(new WxcsSession<WxcsTencentMeetingPerson>((char *)&pMeetingMsg->sessionId, sizeof(pMeetingMsg->sessionId)
+                                                , WXCS_SESSION_CHAT_GROUP_VIDEO, pMeetingMsg->firstActiveTime));
+
+        newSession->addNewPerson(pPerson, bInterestingPerson);
+        tencentMeetingSessionKeeper_.addNewSession(newSession);
+        pTencentMeetingSession = newSession;
+    }
+    else
+    {   // session 存在，检查是否存在该用户
+        auto pPersonIn = pTencentMeetingSession->findPerson(pPerson);
+        if (!pPersonIn)
+        {   // 该用户不存在，添加新用户
+            pTencentMeetingSession->addNewPerson(pPerson, bInterestingPerson);
+        }
+        else
+        {   // 该用户存在，更新用户信息
+            pTencentMeetingSession->updatePersonInfo(pPerson, bInterestingPerson);
+        }
+    }
+
+    LOG_INTST->debug("ProcessTencentMeeting: sessionId={}, selfMeetingNum={}, selfLoginNum={}, msisdn={}",
+                     pMeetingMsg->sessionId, pMeetingMsg->selfMeetingNum, pMeetingMsg->selfLoginNum,
+                     pMeetingMsg->trailer.MSISDN);
+}
 
 /* 消息类型 分发器  */
 void WxcsServer::onMessage(const TcpConnectionPtr& conn _U_,
@@ -1006,6 +1067,9 @@ void WxcsServer::onMessage(const TcpConnectionPtr& conn _U_,
             WXLSKS->ProcessWXLS(pdata, len);
             break;
 
+        case WXCS_TENCENT_MEETING:
+            ProcessTencentMeeting(pdata, len);
+            break;
 
         default:
             return;
@@ -1025,6 +1089,7 @@ void WxcsServer::checkTblWriteTimeout()
     timerLoop_->runAfter(0, std::bind(&WxcsServer::QQVoipTblWriteTimeout, this));
     timerLoop_->runAfter(0, std::bind(&WxcsServer::SkypeTblWriteTimeout, this));
     timerLoop_->runAfter(0, std::bind(&WxcsServer::LocSharingTblWriteTimeout, this));
+    timerLoop_->runAfter(0, std::bind(&WxcsServer::TencentMeetingTblWriteTimeout, this));
 }
 
 
@@ -1067,6 +1132,11 @@ void WxcsServer::LocSharingTblWriteTimeout()
 {
     tblWriteTimeout(locSharingTblWrite_);
 }
+/*腾讯会议写TBL文件超时检测 */
+void WxcsServer::TencentMeetingTblWriteTimeout()
+{
+    tblWriteTimeout(tencentMeetingTblWriter_);
+}
 /*写TBL超时检测通用方法 */
 void WxcsServer::tblWriteTimeout(wxcsTblWriter& tblWriter)
 {
@@ -1112,6 +1182,7 @@ void WxcsServer::checkSessionTimeout()
     timerLoop_->runAfter(5 * checkInterval, std::bind(&WxcsServer::checkQQFileSessionTimeout, this));
     timerLoop_->runAfter(6 * checkInterval, std::bind(&WxcsServer::checkSkypeChatSessionTimeout, this));
     timerLoop_->runAfter(0 * checkInterval, std::bind(&WxcsServer::CheckLocSharingSessionTimeout, this));
+    timerLoop_->runAfter(7 * checkInterval, std::bind(&WxcsServer::checkTencentMeetingSessionTimeout, this));
 }
 
 void WxcsServer:: checkWXASessionTimeout()
@@ -1153,6 +1224,11 @@ void WxcsServer::CheckLocSharingSessionTimeout()
     WXLSKS->removeDeadSessions(std::bind(&WxcsServer::onRemveLocSharingSession, this, _1, _2));
 }
 
+void WxcsServer::checkTencentMeetingSessionTimeout()
+{
+    tencentMeetingSessionKeeper_.removeDeadSessions(std::bind(&WxcsServer::onRemveTencentMeetingSession, this, _1, _2));
+}
+
 int WxcsServer::onRemveGroupHeadSession(uint32_t time _U_, const SessionPtr<WxcsGroupHeadPerson> & session)
 {
     wxghTblWriter_.writeToFile(session);
@@ -1422,6 +1498,38 @@ int WxcsServer::onRemveLocSharingSession(uint32_t time _U_, SessionPtr<WxcsLocSh
     return 0;
 }
 
+// 腾讯会议超时检测
+int WxcsServer::onRemveTencentMeetingSession(uint32_t time _U_, SessionPtr<WxcsTencentMeetingPerson> & session)
+{
+    // 报文数检测 踢出异常的数据(每个Person, UDP数据流的正反双向, 都必须满足N个报文)
+    for (auto it = session->personMap_.begin(); it != session->personMap_.end();)
+    {
+        //判断报文个数
+        static uint32_t minCnt = CFG->GetValueOf<uint32_t>("SESSION_PACKET_AT_LEAST", 20);
+        if(it->second->c2sPackCount < minCnt ||
+           it->second->s2cPackCount < minCnt )
+        {
+            LOG_DEF->debug("tencent meeting session: {}, invalid packet count, c2s: {}, s2c: {}", session->getSessionId(),
+                    it->second->c2sPackCount, it->second->s2cPackCount);
+            it = const_cast<SessionPtr<WxcsTencentMeetingPerson> &>(session)->personMap_.erase(it);
+        }
+        else // 这是一个正常的报文
+        {
+            it++;
+        }
+    }
+
+    // 有人吗 ?
+    if(session->personMap_.empty())
+    {
+        return 0;
+    }
+
+    // 剩下的都是 正常的Person 写入tbl
+    tencentMeetingTblWriter_.writeToFile(session);
+    return 0;
+}
+
 void WxcsServer::handleSignal(int signal)
 {
 	// printf("[%s][%d]receive signal: %d \n", __FILE__, __LINE__, signal);
diff --git a/wxcs/wxcs_server.h b/wxcs/wxcs_server.h
index 350ebb1..8c5a531 100644
--- a/wxcs/wxcs_server.h
+++ b/wxcs/wxcs_server.h
@@ -65,6 +65,7 @@ public:
     void checkQQFileSessionTimeout();
     void checkSkypeChatSessionTimeout();
     void CheckLocSharingSessionTimeout();
+    void checkTencentMeetingSessionTimeout();
 private:
    /*ADD_S by yangna 2020-09-16 */
     void checkWxcsTimeout();
@@ -77,6 +78,7 @@ private:
     void QQVoipTblWriteTimeout();
     void SkypeTblWriteTimeout();
     void LocSharingTblWriteTimeout();
+    void TencentMeetingTblWriteTimeout();
     void tblWriteTimeout(wxcsTblWriter& tblWriter);
 /*ADD_E by yangna 2020-09-16 */
 
@@ -104,6 +106,7 @@ private:
     void ProcessWXRelation(const unsigned char *pdata, int len);
     // void ProcessWXPeer(const unsigned char *pdata, int len);
     void ProcessQQEvent(const unsigned char*pdata, int len);
+    void ProcessTencentMeeting(const unsigned char*pdata, int len);
 
     int onRemveAudioSession(uint32_t time, SessionPtr<WxcsAudioPerson> & session);
     int onRemveZoomSession(uint32_t time, SessionPtr<WxcsZoomPerson> & session);
@@ -114,6 +117,7 @@ private:
     int onRemveQQFileSession(uint32_t time, const SessionPtr<WxcsQQFilePerson> & session);
     int onRemveSkypeChatSession(uint32_t time, SessionPtr<WxcsSkypePerson> & session);
     int onRemveLocSharingSession(uint32_t time, SessionPtr<WxcsLocSharingPerson> & session);
+    int onRemveTencentMeetingSession(uint32_t time, SessionPtr<WxcsTencentMeetingPerson> & session);
 
 private:
     WxcsContentWriter contentWriter_;
@@ -151,6 +155,10 @@ private: // sessions and tbl writer for skype
 private:
     wxcsTblWriter                           locSharingTblWrite_;
 
+private: // sessions and tbl writer for tencent meeting
+    WxcsSessionKeeper<WxcsTencentMeetingPerson> tencentMeetingSessionKeeper_;
+    wxcsTblWriter                           tencentMeetingTblWriter_;
+
 public:/*QQ活动事件处理 */
     WxcsQQEventPerson                       qqEventMap_;
 
diff --git a/wxcs/wxcs_session.cpp b/wxcs/wxcs_session.cpp
index 6f06465..9b77c8c 100644
--- a/wxcs/wxcs_session.cpp
+++ b/wxcs/wxcs_session.cpp
@@ -500,6 +500,45 @@ std::string WxcsSessionBase<WxcsQQGroupPerson>::getColumnList(char sep)
 
     return strColumns;
 }
+
+template<>
+std::string WxcsSessionBase<WxcsTencentMeetingPerson>::getColumnList(char sep)
+{
+#define WX_COLUMN_TEXT(columns, text, sep)     columns += std::string(text) + sep
+
+    std::string strColumns;
+
+    WX_COLUMN_TEXT(strColumns, "DevNo", sep);
+    WX_COLUMN_TEXT(strColumns, "LineNo", sep);
+    WX_COLUMN_TEXT(strColumns, "CapDate", sep);
+    WX_COLUMN_TEXT(strColumns, "SessionType", sep);
+    WX_COLUMN_TEXT(strColumns, "SessionStartTime", sep);
+    WX_COLUMN_TEXT(strColumns, "SessionStopTime", sep);
+    WX_COLUMN_TEXT(strColumns, "SessionDuration", sep);
+    WX_COLUMN_TEXT(strColumns, "PersonCount", sep);
+    WX_COLUMN_TEXT(strColumns, "PersonList", sep);
+
+    for (int i = 0; i < TENCENT_MEETING_MAX_MEMBER_COUNT; i++)
+    {
+        strColumns += WriteTrailerCommon(i, sep);
+        std::string index = (i<=9) ? "0" + std::to_string(i) : std::to_string(i);
+
+        WX_COLUMN_TEXT(strColumns, "PersonLoginID_"         + index, sep);
+        WX_COLUMN_TEXT(strColumns, "PersonMeetingID_"       + index, sep);
+
+        WX_COLUMN_TEXT(strColumns, "PersonC2STransPackets_" + index, sep);
+        WX_COLUMN_TEXT(strColumns, "PersonS2CTransPackets_" + index, sep);
+        WX_COLUMN_TEXT(strColumns, "PersonC2STransBytes_"   + index, sep);
+        WX_COLUMN_TEXT(strColumns, "PersonS2CTransBytes_"   + index, sep);
+
+        WX_COLUMN_TEXT(strColumns, "PersonStartTime_"       + index, sep);
+        WX_COLUMN_TEXT(strColumns, "PersonLastActiveTime_"  + index, sep);
+        WX_COLUMN_TEXT(strColumns, "PersonResv_"            + index, sep);
+    }
+
+    return strColumns;
+}
+
 #ifdef DPI_WXA_WEB
 template<>
 std::string WxcsSessionBase<WxcsQQSinglePerson>::getColumnList(char sep)
@@ -1037,3 +1076,62 @@ std::string WxcsSessionBase<WxcsSkypePerson>::toStrRecordLine(char sep) const
 
     return strLine;
 }
+
+/* template specialization for WxcsTencentMeetingPerson */
+
+
+std::string WxcsSession<WxcsTencentMeetingPerson>::getPersonList() const{
+    std::string strPersonList;
+
+    for (auto kv : personMap_)
+    {
+        strPersonList += "(" + kv.second->getPrintablePersonID() + ',' + kv.second->selfMeetingNum + ")" + ",";
+    }
+
+    return strPersonList;
+}
+
+std::string WxcsSession<WxcsTencentMeetingPerson>::toStrRecordLine(char sep) const
+{
+    std::string strLine;
+
+
+    uint32_t firstActiveTime = 0;
+    uint32_t lastActiveTime = 0;
+
+    for (auto& kv : personMap_)
+    {
+        if (firstActiveTime == 0 || kv.second->firstActiveTime < firstActiveTime)
+            firstActiveTime = kv.second->firstActiveTime;
+        if (kv.second->lastActiveTime > lastActiveTime)
+            lastActiveTime = kv.second->lastActiveTime;
+    }
+    // 写入头部信息
+    CONS_RECORD_FIELD_TEXT(strLine, "tencent_meeting",               sep); // DevNO
+    CONS_RECORD_FIELD_TEXT(strLine, "001",                          sep); // LineNO
+    CONS_RECORD_FIELD_TIME(strLine, time(NULL),                     sep); // CapDate
+    CONS_RECORD_FIELD_NUM(strLine  , sessionType_                                , sep); // SessionType
+    CONS_RECORD_FIELD_TIME(strLine , firstActiveTime                                  , sep); // SessionStartTime
+    CONS_RECORD_FIELD_TIME(strLine , lastActiveTime                                   , sep); // SessionStopTime
+    CONS_RECORD_FIELD_NUM(strLine  , lastActiveTime - firstActiveTime                       , sep); // SessionDuration
+    CONS_RECORD_FIELD_NUM(strLine  , personMap_.size()                                , sep); // PersonCount
+    CONS_RECORD_FIELD(strLine      , getPersonList()                            , sep); // PersonList
+    // 写入每个人的信息
+    int iChecked = 0;
+    for (auto& kv : personMap_)
+    {
+        if (iChecked >= SESSION_PERSON_COUNT_MAX)
+            break;
+
+        strLine += kv.second->toStrRecord(sep);
+        iChecked++;
+    }
+
+    // 人数不够, TBL 补齐
+    for (; iChecked < SESSION_PERSON_COUNT_MAX; iChecked++)
+    {
+        strLine += WxcsTencentMeetingPerson::toStrBlankRecord(sep);
+    }
+
+    return strLine;
+}
diff --git a/wxcs/wxcs_session.h b/wxcs/wxcs_session.h
index 170b00f..c09a046 100644
--- a/wxcs/wxcs_session.h
+++ b/wxcs/wxcs_session.h
@@ -312,6 +312,20 @@ private:
     std::set<uint64_t> extraQQSet_;
 };
 
+template<>
+class WxcsSession<WxcsTencentMeetingPerson> : public WxcsSessionBase<WxcsTencentMeetingPerson>
+{
+public:
+    using WxcsSessionBase<WxcsTencentMeetingPerson>::WxcsSessionBase;
+
+public:
+    // 特化基类方法的满足不了需求，这里选择在子类重写
+    std::string toStrRecordLine(char sep) const;
+
+    std::string getPersonList() const;
+
+};
+
 // WxcsSession for WxcsQQSinglePerson
 template<>
 class WxcsSession<WxcsQQSinglePerson> : public WxcsSessionBase<WxcsQQSinglePerson>
