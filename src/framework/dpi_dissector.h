/****************************************************************************************
 * 文 件 名 : dpi_dissector.h
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy  			2018/07/06
编码: wangy			2018/07/06
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_DISSECTOR_H_
#define _DPI_DISSECTOR_H_


void init_http_dissector(void);
void init_qq_voip_chat_dissector(void);
void init_skype_media_dissector(void);
void init_WeixinMediaChat_dissector(void);
void init_wx_location_sharing_dissector(void);
void init_weixin_dissector(void);
void init_ZOOM_Conference_dissector(void);
void init_weixin_rela_dissector(void);
void init_weixin_info_dissector(void);
void init_weixin_peers_dissector(void);
void init_qq_event_dissector(void);
void init_weixin_misc_dissector(void);
void init_gquic_dissector(void);
void fini_http_dissector(void);
void init_tencent_meeting_chat_dissector(void);
int init_tencent_meeting_user_thread(void);
int wait_tencent_meeting_user_thread_finish(void);
#endif
