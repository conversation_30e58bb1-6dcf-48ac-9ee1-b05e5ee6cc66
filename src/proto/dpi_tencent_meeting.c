#include "dpi_detect.h"
#include "dpi_log.h"
#include "dpi_cjson.h"
#include "dpi_common.h"
#include "dpi_utils.h"
#include "wxcs_def.h"
#include <glib.h>
#include <pthread.h>
#include <ctype.h>
#include <stdbool.h>
#include <stdlib.h>

extern struct global_config g_config;

// 全局变量
static wxc_handle       g_tencent_meeting_handle = NULL;
static GHashTable      *g_tencent_meeting_user_hash = NULL;
static pthread_rwlock_t g_tencent_meeting_rwlock = PTHREAD_RWLOCK_INITIALIZER;
static pthread_t        g_tencent_meeting_thread;

// 用户信息结构体，用于关联个人常规账号和个人会议账号
typedef struct {
  char     msisdn[16];
  char     ip[32];
  char     selfLoginNum[18];
  uint32_t lastActiveTime;
} TencentMeetingUser;

// protobuf解析函数声明
uint8_t decode_protobuf(char *input, int input_size, char **output);

// JSON字符串清理函数
static char *clean_json_string(const char *raw_json) {
  if (!raw_json)
    return NULL;

  size_t len = strlen(raw_json);
  char  *cleaned = (char *)malloc(len * 2 + 1);  // 预留足够空间
  if (!cleaned)
    return NULL;

  size_t write_pos = 0;
  bool   in_string = false;
  bool   escape_next = false;

  for (size_t i = 0; i < len; i++) {
    char c = raw_json[i];

    // 跳过非打印字符（除了必要的JSON字符）
    if (!in_string && (c < 32 || c > 126)) {
      if (c != '\n' && c != '\r' && c != '\t') {
        continue;
      }
    }

    if (escape_next) {
      // 处理转义字符
      cleaned[write_pos++] = '\\';
      if (c == '"' || c == '\\' || c == '/' || c == 'b' || c == 'f' || c == 'n' || c == 'r' || c == 't') {
        cleaned[write_pos++] = c;
      } else if (c >= 32 && c <= 126) {
        cleaned[write_pos++] = c;
      }
      escape_next = false;
      continue;
    }

    if (c == '\\') {
      escape_next = true;
      continue;
    }

    if (c == '"' && !escape_next) {
      in_string = !in_string;
    }

    // 在字符串内部，过滤掉不可打印的二进制字符
    if (in_string && (c < 32 || c > 126)) {
      if (c == '\n') {
        cleaned[write_pos++] = '\\';
        cleaned[write_pos++] = 'n';
      } else if (c == '\r') {
        cleaned[write_pos++] = '\\';
        cleaned[write_pos++] = 'r';
      } else if (c == '\t') {
        cleaned[write_pos++] = '\\';
        cleaned[write_pos++] = 't';
      }
      // 其他不可打印字符直接跳过
      continue;
    }

    cleaned[write_pos++] = c;
  }

  cleaned[write_pos] = '\0';
  return cleaned;
}

// 修复JSON格式错误的函数
static char *fix_json_format(const char *json_str) {
  if (!json_str)
    return NULL;

  size_t len = strlen(json_str);
  char  *fixed = (char *)malloc(len * 2 + 1);
  if (!fixed)
    return NULL;

  size_t write_pos = 0;
  bool   in_string = false;
  bool   need_comma = false;

  for (size_t i = 0; i < len; i++) {
    char c = json_str[i];

    if (c == '"' && (i == 0 || json_str[i - 1] != '\\')) {
      in_string = !in_string;
      if (!in_string && need_comma) {
        // 字符串结束后，检查是否需要添加逗号
        size_t j = i + 1;
        while (j < len && (json_str[j] == ' ' || json_str[j] == '\n' || json_str[j] == '\r' || json_str[j] == '\t')) {
          j++;
        }
        if (j < len && json_str[j] == '"') {
          fixed[write_pos++] = c;
          fixed[write_pos++] = ',';
          need_comma = false;
          continue;
        }
      }
      need_comma = !in_string;
    }

    // 修复缺失的冒号
    if (!in_string && c == '"' && i + 1 < len) {
      size_t j = i + 1;
      while (j < len && json_str[j] != '"') j++;
      if (j < len) {
        j++;  // 跳过结束引号
        while (j < len && (json_str[j] == ' ' || json_str[j] == '\n' || json_str[j] == '\r' || json_str[j] == '\t')) {
          j++;
        }
        if (j < len && json_str[j] == '"' && (j == 0 || json_str[j - 1] != ':')) {
          // 在两个字符串之间缺少冒号
          fixed[write_pos++] = c;
          // 复制到下一个引号
          for (size_t k = i + 1; k <= j - 1; k++) {
            if (k < len)
              fixed[write_pos++] = json_str[k];
          }
          fixed[write_pos++] = ':';
          i = j - 1;
          continue;
        }
      }
    }

    fixed[write_pos++] = c;
  }

  fixed[write_pos] = '\0';
  return fixed;
}

// 专门处理protobuf转JSON后的格式问题
static char *fix_protobuf_json(const char *raw_json) {
  if (!raw_json)
    return NULL;

  // 示例输入: {\n\"1\":\"2\":\n\"6\":"\263\"}
  // 期望输出: {"1":"2","6":"value"}

  size_t len = strlen(raw_json);
  char  *result = (char *)malloc(len * 2 + 1);
  if (!result)
    return NULL;

  size_t write_pos = 0;
  bool   in_string = false;
  bool   after_colon = false;

  for (size_t i = 0; i < len; i++) {
    char c = raw_json[i];

    // 跳过换行符和回车符（除非在字符串内）
    if (!in_string && (c == '\n' || c == '\r')) {
      continue;
    }

    // 处理转义的引号
    if (c == '\\' && i + 1 < len && raw_json[i + 1] == '"') {
      result[write_pos++] = '"';
      i++;  // 跳过下一个字符
      if (!in_string) {
        in_string = true;
      } else {
        in_string = false;
        after_colon = false;
      }
      continue;
    }

    // 处理普通引号
    if (c == '"') {
      in_string = !in_string;
      result[write_pos++] = c;
      if (!in_string) {
        after_colon = false;
      }
      continue;
    }

    // 处理冒号
    if (!in_string && c == ':') {
      result[write_pos++] = c;
      after_colon = true;
      continue;
    }

    // 在字符串内部处理二进制字符
    if (in_string) {
      // 如果是不可打印字符，转换为十六进制表示或跳过
      if ((unsigned char)c < 32 || (unsigned char)c > 126) {
        if (c == '\t') {
          result[write_pos++] = '\\';
          result[write_pos++] = 't';
        } else if (c == '\n') {
          result[write_pos++] = '\\';
          result[write_pos++] = 'n';
        } else if (c == '\r') {
          result[write_pos++] = '\\';
          result[write_pos++] = 'r';
        } else {
          // 对于其他二进制字符，转换为十六进制或替换为安全字符
          result[write_pos++] = '?';  // 或者可以用sprintf转为\uXXXX格式
        }
      } else {
        result[write_pos++] = c;
      }
      continue;
    }

    // 检查是否需要在两个字符串值之间添加逗号
    if (!in_string && after_colon && c == '"') {
      // 这可能是一个新的键开始，需要先结束当前值
      result[write_pos++] = '"';
      result[write_pos++] = ',';
      after_colon = false;
      continue;
    }

    // 其他字符直接复制
    result[write_pos++] = c;
  }

  result[write_pos] = '\0';
  return result;
}

// 简单的字符串解析方法，用于从损坏的JSON中提取数值
static uint64_t extract_meeting_number_simple(const char *json_str) {
  if (!json_str)
    return 0;

  // 查找模式 "2": 后面的数字，或者 "3": 后面的数字
  const char *patterns[] = {"\"2\":", "\"3\":", NULL};

  for (int p = 0; patterns[p]; p++) {
    const char *pos = strstr(json_str, patterns[p]);
    if (pos) {
      pos += strlen(patterns[p]);

      // 跳过空白字符
      while (*pos && (*pos == ' ' || *pos == '\t' || *pos == '\n' || *pos == '\r')) {
        pos++;
      }

      // 查找数字
      if (*pos == '"') {
        pos++;  // 跳过引号
        char *end_quote = strchr(pos, '"');
        if (end_quote) {
          char   temp[32];
          size_t len = end_quote - pos;
          if (len < sizeof(temp)) {
            memcpy(temp, pos, len);
            temp[len] = '\0';
            return strtoull(temp, NULL, 10);
          }
        }
      } else if (isdigit(*pos)) {
        return strtoull(pos, NULL, 10);
      }
    }
  }

  return 0;
}

// 腾讯会议信息解析接口
typedef struct {
  uint64_t meetingNum;
  uint64_t sessionId;
  int      parseResult;  // 0: 失败, 1: 成功
} TencentMeetingInfo;

// 抽象的腾讯会议信息解析接口
static int parse_tencent_meeting_info(
    const uint8_t *payload, uint32_t payload_len, uint32_t info_offset, uint32_t info_len, TencentMeetingInfo *meeting_info) {
  if (!payload || !meeting_info || info_len == 0) {
    return 0;
  }

  memset(meeting_info, 0, sizeof(TencentMeetingInfo));

  // 解析protobuf格式的info部分
  char   *json_output = NULL;
  uint8_t decode_result = decode_protobuf((char *)(payload + info_offset), info_len, &json_output);

  if (decode_result && json_output) {
    // 使用专门的protobuf JSON修复函数
    char *fixed_json = fix_protobuf_json(json_output);
    if (fixed_json) {
      DPI_LOG(DPI_LOG_DEBUG, "Original JSON: %s", json_output);
      DPI_LOG(DPI_LOG_DEBUG, "Fixed JSON: %s", fixed_json);

      cJSON *json = dpi_cjson_parse_json(fixed_json);
      if (json) {
        // 获取个人会议账号 {"2":{"3": xxxxxxxxxxxx}}
        cJSON *node2 = dpi_cjson_get_object_field(json, "2");
        if (node2) {
          cJSON *node3 = dpi_cjson_get_object_field(node2, "3");
          if (node3 && cJSON_IsNumber(node3)) {
            meeting_info->meetingNum = (uint64_t)node3->valuedouble;
            meeting_info->parseResult = 1;
          }
        }
        dpi_cjson_free_json(json);
      } else {
        // JSON解析仍然失败，尝试其他方法
        DPI_LOG(DPI_LOG_DEBUG, "JSON parse failed, trying alternative cleanup");
        char *alt_cleaned = clean_json_string(json_output);
        if (alt_cleaned) {
          char *alt_fixed = fix_json_format(alt_cleaned);
          if (alt_fixed) {
            cJSON *alt_json = dpi_cjson_parse_json(alt_fixed);
            if (alt_json) {
              cJSON *node2 = dpi_cjson_get_object_field(alt_json, "2");
              if (node2) {
                cJSON *node3 = dpi_cjson_get_object_field(node2, "3");
                if (node3 && cJSON_IsNumber(node3)) {
                  meeting_info->meetingNum = (uint64_t)node3->valuedouble;
                  meeting_info->parseResult = 1;
                }
              }
              dpi_cjson_free_json(alt_json);
            }
            free(alt_fixed);
          }
          free(alt_cleaned);
        }

        // 如果所有JSON解析方法都失败，使用简单字符串解析
        if (meeting_info->parseResult == 0) {
          uint64_t simple_result = extract_meeting_number_simple(json_output);
          if (simple_result > 0) {
            meeting_info->meetingNum = simple_result;
            meeting_info->parseResult = 1;
            DPI_LOG(DPI_LOG_DEBUG, "Used simple parsing, extracted: %lu", simple_result);
          }
        }
      }
      free(fixed_json);
    } else {
      // 如果修复失败，直接尝试简单解析
      uint64_t simple_result = extract_meeting_number_simple(json_output);
      if (simple_result > 0) {
        meeting_info->meetingNum = simple_result;
        meeting_info->parseResult = 1;
        DPI_LOG(DPI_LOG_DEBUG, "Used fallback simple parsing, extracted: %lu", simple_result);
      }
    }
    free(json_output);
  }

  return meeting_info->parseResult;
}

typedef struct Tencent_meeting_session_t {
  int      FlagPacketC2S;
  int      FlagPacketS2C;
  int      SessionType;
  uint64_t sessionId;
  char     selfMeetingNum[18];
  char     selfLoginNum[18];
  uint32_t firstActiveTime;
  uint32_t lastActiveTime;
  uint32_t c2sPackCount;
  uint32_t c2sByteCount;
  uint32_t s2cPackCount;
  uint32_t s2cByteCount;
} Tencent_meeting_session;

// 初始化全局hash表
static void init_tencent_meeting_hash(void) {
  pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);
  if (!g_tencent_meeting_user_hash) {
    g_tencent_meeting_user_hash = g_hash_table_new_full(g_str_hash, g_str_equal, g_free, g_free);
    if (!g_tencent_meeting_user_hash) {
      DPI_LOG(DPI_LOG_ERROR, "error on create tencent meeting user hash");
      exit(-1);
    }
  }
  pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
}

// 查找用户信息
static TencentMeetingUser *find_user_by_msisdn_or_ip(char *msisdn, char *ip) {
  TencentMeetingUser *user = NULL;

  pthread_rwlock_rdlock(&g_tencent_meeting_rwlock);
  if (g_tencent_meeting_user_hash) {
    // 优先通过MSISDN查找
    if (msisdn != 0) {
      user = (TencentMeetingUser *)g_hash_table_lookup(g_tencent_meeting_user_hash, (gconstpointer)(msisdn));
    }
    // 如果MSISDN找不到，通过IP查找
    if (!user && ip != 0) {
      user = (TencentMeetingUser *)g_hash_table_lookup(g_tencent_meeting_user_hash, (gconstpointer)(ip));
    }
  }
  pthread_rwlock_unlock(&g_tencent_meeting_rwlock);

  return user;
}

// 添加或更新用户信息
static void add_or_update_user(char *msisdn, char *ip, const char *selfLoginNum) {
  pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);
  if (g_tencent_meeting_user_hash) {
    TencentMeetingUser *user = (TencentMeetingUser *)malloc(sizeof(TencentMeetingUser));
    if (user) {
      strcpy(user->msisdn, msisdn);
      strcpy(user->ip, ip);
      if (selfLoginNum) {
        strncpy(user->selfLoginNum, selfLoginNum, sizeof(user->selfLoginNum) - 1);
        user->selfLoginNum[sizeof(user->selfLoginNum) - 1] = '\0';
      } else {
        user->selfLoginNum[0] = '\0';
      }
      user->lastActiveTime = time(NULL);

      // 使用MSISDN作为key，如果没有MSISDN则使用IP
      char *key_str = (msisdn[0] != 0 ? g_strdup(msisdn) : g_strdup(ip));
      g_hash_table_replace(g_tencent_meeting_user_hash, (gpointer)key_str, user);
    }
  }
  pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
}

// 删除超时用户信息 (线程不安全)
static int tencent_meeting_hash_delete_unthread_safe(void* hash, char *pStr) {
  if (NULL == hash || NULL == pStr) {
    return -1;
  }
  int ret = (int)g_hash_table_remove((GHashTable*)hash, (gconstpointer)pStr);
  return 0;
}

// 获取hash表大小
static size_t tencent_meeting_hash_get_size(void* hash) {
  if (NULL == hash) {
    return 0;
  }
  pthread_rwlock_rdlock(&g_tencent_meeting_rwlock);
  size_t ret = g_hash_table_size((GHashTable *)hash);
  pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
  return ret;
}

// 腾讯会议用户管理线程
static void *tencent_meeting_user_thread(void *arg) {
#define MAX_TIME_OUT_NODE 1024*256

  // 命运的轮回...
  while(1) {
    sleep(g_config.wx_session_timeloop);

    int   hash_count = 0;
    int   KeyTop     = 0;
    int   send       = 0;
    int   relation   = 0;
    void* Stack[MAX_TIME_OUT_NODE]; // 用来存储超时的 hash node

    char* key = NULL;
    TencentMeetingUser* value = NULL;

    // 遍历所有用户节点
    pthread_rwlock_rdlock(&g_tencent_meeting_rwlock);
    if (g_tencent_meeting_user_hash) {
      GHashTableIter iter;
      g_hash_table_iter_init(&iter, g_tencent_meeting_user_hash);
      while(g_hash_table_iter_next(&iter, (gpointer*)&key, (gpointer*)&value)) {
        // 有效性检测
        if (NULL == value) {
          continue;
        }

        // 超时判断
        if (time(NULL) - value->lastActiveTime > g_config.wx_session_timeout) {
          if (KeyTop < MAX_TIME_OUT_NODE && NULL != key) {
            Stack[KeyTop++] = key; // 记录待删除的key
          }
        }

        // 统计总人数
        ++hash_count;

        // 统计建联人数 (有MSISDN的用户)
        if (value->msisdn[0] != '\0') {
          ++relation;
        }

        // 统计发送人数 (有个人常规账号的用户)
        if (value->selfLoginNum[0] != '\0') {
          ++send;
        }
      }
    }
    pthread_rwlock_unlock(&g_tencent_meeting_rwlock);

    // 打印统计信息
    char buffer[26];
    time_t timer;
    time(&timer);
    struct tm* tm_info = localtime(&timer);
    strftime(buffer, 26, "%Y-%m-%d %H:%M:%S", tm_info);
    printf("TECENT_MEETING :[%s] 已发送/已超时/已建联/总人数=[%d/%d/%d/%d]\n", buffer, send, KeyTop, relation, hash_count);

    // 开始删除超时的用户节点
    pthread_rwlock_wrlock(&g_tencent_meeting_rwlock);
    if (g_tencent_meeting_user_hash) {
      while(KeyTop--) {
        tencent_meeting_hash_delete_unthread_safe(g_tencent_meeting_user_hash, Stack[KeyTop]);
      }
    }
    pthread_rwlock_unlock(&g_tencent_meeting_rwlock);
  }

  return NULL;
}

static void update_session_time(Tencent_meeting_session *session) {
  // 更新会话信息
  session->lastActiveTime = time(NULL);
  if (session->firstActiveTime == 0) {
    session->firstActiveTime = session->lastActiveTime;
  }
}
// 解析个人会议数据 (0x28开头)
static int dissect_tencent_meeting_28(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  if (payload_len < 20) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 解析Header长度和Info长度
  uint32_t headerLen = get_uint32_ntohl(payload, 1);
  uint32_t infoLen = get_uint32_ntohl(payload, 5);

  if (payload_len < 9 + headerLen + infoLen + 1) {
    return 0;
  }

  // 检查结尾是否为0x29
  if (payload[9 + headerLen + infoLen] != 0x29) {
    return 0;
  }

  // 使用抽象接口解析会议信息
  TencentMeetingInfo meeting_info;
  if (parse_tencent_meeting_info(payload, payload_len, 9, headerLen, &meeting_info)) {
    snprintf(session->selfMeetingNum, sizeof(session->selfMeetingNum), "%lu", meeting_info.meetingNum);

    update_session_time(session);
  }

  return 0;
}
// 解析个人会议数据 (0x36开头)
static int dissect_tencent_meeting_36(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  if (payload_len < 20) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 解析包长度
  uint16_t packetLen = get_uint16_ntohs(payload, 1);
  if (payload_len < packetLen) {
    return 0;
  }

  // 检查固定字段
  if (get_uint32_ntohl(payload, 3) != 0x00000000) {
    return 0;
  }

  // 检查0x03a102
  if (get_uint16_ntohs(payload, 7) != 0x03a1 || payload[9] != 0x02) {
    return 0;
  }

  // 解析sessionID (4字节)
  uint32_t sessionID = get_uint32_ntohl(payload, 17);
  session->sessionId = sessionID;

  update_session_time(session);

  return 0;
}
// 解析个人会议数据 (0x51开头)
static int dissect_tencent_meeting_51(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  if (payload_len < 16) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 检查固定字段 0x0011
  if (get_uint16_ntohs(payload, 1) != 0x0011) {
    return 0;
  }

  // 检查0x02
  if (payload[5] != 0x02) {
    return 0;
  }

  // 解析sessionID (4字节)
  uint32_t sessionID = get_uint32_ntohl(payload, 13);
  session->sessionId = sessionID;

  update_session_time(session);
  return 0;
}
// 解析个人常规账号 (TCP)
static int dissect_tencent_meeting_tcp(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  if (payload_len < 32) {
    return 0;
  }

  // 检查标识符 0x00001770
  if (get_uint32_ntohl(payload, 4) != 0x00001770) {
    return 0;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }

  // 解析个人常规账号
  char     selfLoginNum[19] = {0};
  uint32_t accountOffset = 0;

  // 根据方向确定账号位置
  if (direction == 0) {
    uint64_t info_len = 0;
    info_len = get_uint32_ntohl(payload, 9);
    if (info_len >= payload_len - 9) {
      return 0;
    }
    if (get_uint32_ntohl(payload, info_len + 9) == 0x14000000 && get_uint16_ntohs(payload, info_len + 9 + 3) == 0x0016) {  // 上行
      // 查找0x1400000016标识
      accountOffset = info_len + 14;
    }
  } else {  // 下行
            // 查找0x1400000016标识
    if (get_uint32_ntohl(payload, 9) == 0x14000000 && get_uint16_ntohs(payload, 9 + 3) == 0x0016) {
      accountOffset = 14;
    }
  }

  if (accountOffset > 0 && accountOffset + 18 <= payload_len) {
    memcpy(selfLoginNum, payload + accountOffset, 18);
    selfLoginNum[18] = '\0';
    strncpy(session->selfLoginNum, selfLoginNum, sizeof(session->selfLoginNum) - 1);
    session->selfLoginNum[sizeof(session->selfLoginNum) - 1] = '\0';

    // 解析trailer获取MSISDN
    ST_trailer tmp_trailer;
    memset(&tmp_trailer, 0, sizeof(ST_trailer));

    dpi_TrailerParser(&tmp_trailer, (const char *)flow->trailer, flow->trailerlen, g_config.trailertype);

    char msisdn[16] = {0};
    snprintf(msisdn, sizeof(msisdn), "%lu", tmp_trailer.MSISDN);

    // 获取IP地址
    char ip[32] = {0};
    get_ipstring(flow->tuple.inner.ip_version, (char *)&flow->tuple.inner.ip_src, ip, sizeof(ip));
    // 添加到全局hash表
    add_or_update_user(msisdn, ip, selfLoginNum);

    update_session_time(session);
  }

  return 0;
}
static int dissect_tencent_meeting_43(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len) {
  if (payload_len < 16) {
    return 0;
  }
  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;
  if (!session) {
    return 0;
  }
  uint16_t len = get_uint16_ntohs(payload, 1);
  uint8_t  tag = payload[3];
  if (tag == 0x51) {
    int result = dissect_tencent_meeting_51(flow, direction, seq, payload + 3, payload_len - 3);
  } else if (tag == 0x41) {
    update_session_time(session);
  }

  return 0;
}

// 发送腾讯会议数据到wxcs
static void send_tencent_meeting_data(struct flow_info *flow, Tencent_meeting_session *session, uint8_t direction) {
  if (!session || !g_tencent_meeting_handle) {
    return;
  }

  ST_TencentMeeting person_info;
  memset(&person_info, 0, sizeof(ST_TencentMeeting));

  // 解析trailer
  dpi_TrailerParser(&person_info.trailer, (const char *)flow->trailer, flow->trailerlen, g_config.trailertype);
  dpi_TrailerGetMAC(&person_info.trailer, (const char *)flow->ethhdr, g_config.RT_model);
  dpi_TrailerGetHWZZMAC(&person_info.trailer, (const char *)flow->ethhdr);
  dpi_TrailerSetDev(&person_info.trailer, g_config.devname);
  dpi_TrailerSetOpt(&person_info.trailer, g_config.operator_name);
  dpi_TrailerSetArea(&person_info.trailer, g_config.devArea);

  person_info.ip_version = flow->tuple.inner.ip_version;
  // 设置IP和端口信息
  if (direction == 0) {  // C2S
    if (person_info.ip_version == 4) {
      person_info.srcIp = flow->tuple.inner.ip_src.ip4;
      person_info.dstIp = flow->tuple.inner.ip_dst.ip4;
    } else {
      memcpy(&person_info.srcIp6, &flow->tuple.inner.ip_src, sizeof(flow->tuple.inner.ip_dst));
      memcpy(&person_info.dstIp6, &flow->tuple.inner.ip_dst, sizeof(flow->tuple.inner.ip_dst));
    }
    person_info.srcPort = ntohs(flow->tuple.inner.port_src);
    person_info.dstPort = ntohs(flow->tuple.inner.port_dst);
  } else {  // S2C
    if (person_info.ip_version == 4) {
      person_info.dstIp = flow->tuple.inner.ip_src.ip4;
      person_info.srcIp = flow->tuple.inner.ip_dst.ip4;
    } else {
      memcpy(&person_info.dstIp6, &flow->tuple.inner.ip_src, sizeof(flow->tuple.inner.ip_dst));
      memcpy(&person_info.srcIp6, &flow->tuple.inner.ip_dst, sizeof(flow->tuple.inner.ip_dst));
    }
    person_info.srcPort = ntohs(flow->tuple.inner.port_dst);
    person_info.dstPort = ntohs(flow->tuple.inner.port_src);
  }

  // 设置会话信息
  person_info.sessionId = session->sessionId;
  strncpy(person_info.selfMeetingNum, session->selfMeetingNum, sizeof(person_info.selfMeetingNum) - 1);
  strncpy(person_info.selfLoginNum, session->selfLoginNum, sizeof(person_info.selfLoginNum) - 1);

  person_info.firstActiveTime = session->firstActiveTime;
  person_info.lastActiveTime = session->lastActiveTime;
  person_info.c2sPackCount = session->c2sPackCount;
  person_info.c2sByteCount = session->c2sByteCount;
  person_info.s2cPackCount = session->s2cPackCount;
  person_info.s2cByteCount = session->s2cByteCount;
  person_info.isTimeout = 0;

  // 尝试关联个人常规账号
  char msisdn[16] = {0};
  snprintf(msisdn, sizeof(msisdn), "%lu", person_info.trailer.MSISDN);
  char ip_str[32] = {0};
  if (person_info.ip_version == 4) {
    get_ipstring(person_info.ip_version, (char *)&person_info.srcIp, ip_str, sizeof(ip_str));
  } else {
    get_ipstring(person_info.ip_version, (char *)&person_info.srcIp6, ip_str, sizeof(ip_str));
  }

  TencentMeetingUser *user = find_user_by_msisdn_or_ip(msisdn, ip_str);
  if (user && user->selfLoginNum[0] != '\0') {
    strncpy(person_info.selfLoginNum, user->selfLoginNum, sizeof(person_info.selfLoginNum) - 1);
  }

  // 发送数据
  wxc_sendMsg(g_tencent_meeting_handle, (const unsigned char *)&person_info, sizeof(ST_TencentMeeting), WXCS_TENCENT_MEETING);
}

static int dissect_tencent_meeting_chat(
    struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag) {
  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
    return 0;
  }

  if (NULL == flow->app_session) {
    Tencent_meeting_session *tcmeeting_session;
    tcmeeting_session = malloc(sizeof(Tencent_meeting_session));
    if (NULL == tcmeeting_session) {
      DPI_LOG(DPI_LOG_ERROR, "error on malloc Tencent_meeting_session");
      return 0;
    }
    memset(tcmeeting_session, 0, sizeof(Tencent_meeting_session));
    flow->app_session = tcmeeting_session;
  }

  Tencent_meeting_session *session = (Tencent_meeting_session *)flow->app_session;

  uint16_t dstPort = ntohs(flow->tuple.inner.port_dst);
  if (dstPort == 443 || dstPort == 8080 || dstPort == 80) {
    direction = 0;
  } else {
    direction = 1;
  }

  // 更新包计数
  if (direction == 0) {  // C2S
    session->c2sPackCount++;
    session->c2sByteCount += payload_len;
  } else {  // S2C
    session->s2cPackCount++;
    session->s2cByteCount += payload_len;
  }

  int result = 0;
  switch (payload[0]) {
    case 0x28:
      result = dissect_tencent_meeting_28(flow, direction, seq, payload, payload_len);
      break;
    case 0x36:
      result = dissect_tencent_meeting_36(flow, direction, seq, payload, payload_len);
      break;
    case 0x51:
      result = dissect_tencent_meeting_51(flow, direction, seq, payload, payload_len);
      break;
    case 0x43:
      result = dissect_tencent_meeting_43(flow, direction, seq, payload, payload_len);
      break;
    case 0x41:
      update_session_time(session);
      return 0;
      break;
    default:
      if (get_uint32_ntohl(payload, 4) != 0x00001770)
        return 0;
      result = dissect_tencent_meeting_tcp(flow, direction, seq, payload, payload_len);
      break;
  }

  // 如果解析到了sessionID和个人会议账号，发送数据
  if (session->sessionId != 0 && session->selfMeetingNum[0] != '\0') {
    send_tencent_meeting_data(flow, session, direction);
  }

  return result;
}

static void identify_tencent_meeting_chat(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len) {
  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
    return;
  }

  if (payload_len < 20) {
    return;
  }
  /* 判断报文的目标端口  */
  int port_src = ntohs(flow->tuple.inner.port_src);
  int port_dst = ntohs(flow->tuple.inner.port_dst);
  if (flow->tuple.inner.proto == 6) {
    if ((80 == port_dst || 80 == port_src || 443 == port_dst || 443 == port_src || 8080 == port_dst || 8080 == port_src) &&
        (get_uint32_ntohl(payload, 4) == 0x00001770)) {
      //tcp个人常规
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    } else if ((443 == port_dst || 443 == port_src) && (payload[0] == 0x28)) {
      // tcp个人会议
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    }
  } else {
    if ((443 == port_dst || 443 == port_src) &&
        (payload[0] == 0x28 || payload[0] == 36 || payload[0] == 28 || payload[0] == 41 || payload[0] == 51)) {
      flow->real_protocol_id = PROTOCOL_TENCENT_MEETING;
    }
    return;
  }
}

// 初始化腾讯会议用户管理线程
int init_tencent_meeting_user_thread(void) {
  // 如果腾讯会议功能没有开启
  if (g_config.protocol_switch[PROTOCOL_TENCENT_MEETING] == 0) {
    return 0;
  }

  int status = pthread_create(&g_tencent_meeting_thread, NULL, tencent_meeting_user_thread, NULL);
  if (status != 0) {
    DPI_LOG(DPI_LOG_ERROR, "error on create tencent_meeting_user thread");
    exit(-1);
  }

  return 0;
}

// 等待腾讯会议用户管理线程结束
int wait_tencent_meeting_user_thread_finish(void) {
  return pthread_join(g_tencent_meeting_thread, NULL);
}

void init_tencent_meeting_chat_dissector(void) {
  // 初始化wxc连接
  if (NULL == g_tencent_meeting_handle) {
    wxc_init(&g_tencent_meeting_handle, g_config.wx_voice_ip, g_config.wx_voice_port);
  }

  // 初始化hash表
  init_tencent_meeting_hash();

  // 启动用户管理线程
  init_tencent_meeting_user_thread();

  port_add_proto_head(IPPROTO_UDP, 443, PROTOCOL_TENCENT_MEETING);

  udp_detection_array[PROTOCOL_TENCENT_MEETING].proto = PROTOCOL_TENCENT_MEETING;
  udp_detection_array[PROTOCOL_TENCENT_MEETING].identify_func = identify_tencent_meeting_chat;
  udp_detection_array[PROTOCOL_TENCENT_MEETING].dissect_func = dissect_tencent_meeting_chat;
  port_add_proto_head(IPPROTO_TCP, 80, PROTOCOL_TENCENT_MEETING);
  port_add_proto_head(IPPROTO_TCP, 8080, PROTOCOL_TENCENT_MEETING);
  port_add_proto_head(IPPROTO_TCP, 443, PROTOCOL_TENCENT_MEETING);

  tcp_detection_array[PROTOCOL_TENCENT_MEETING].proto = PROTOCOL_TENCENT_MEETING;
  tcp_detection_array[PROTOCOL_TENCENT_MEETING].identify_func = identify_tencent_meeting_chat;
  tcp_detection_array[PROTOCOL_TENCENT_MEETING].dissect_func = dissect_tencent_meeting_chat;

  DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_TENCENT_MEETING].excluded_protocol_bitmask, PROTOCOL_TENCENT_MEETING);
  DPI_SAVE_AS_BITMASK(tcp_detection_array[PROTOCOL_TENCENT_MEETING].excluded_protocol_bitmask, PROTOCOL_TENCENT_MEETING);

  return;
}
